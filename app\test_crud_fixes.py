#!/usr/bin/env python3
"""
Test script to verify the fixes in generated_crud.py
"""

import os
import sys
from app.crud import SECDBManager

def test_connection_handling():
    """Test that connection handling works properly."""
    print("Testing connection handling...")
    
    # Test with invalid connection string
    try:
        db = SECDBManager("postgresql://invalid:invalid@localhost:5432/invalid")
        print("❌ Should have failed with invalid connection")
        return False
    except Exception as e:
        print(f"✅ Correctly failed with invalid connection: {e}")
    
    # Test with valid connection string from environment
    db_url = os.getenv('DATABASE_URL', '***********************************************/myapp')
    
    try:
        db = SECDBManager(db_url)
        print("✅ Successfully created database manager")
        
        # Test basic operations
        result = db.get_exchange("NASDAQ")
        print(f"✅ get_exchange works: {result is not None}")
        
        # Test search
        companies = db.search_companies("test")
        print(f"✅ search_companies works: {type(companies)} with {len(companies)} results")
        
        db.disconnect()
        print("✅ Successfully disconnected")
        return True
        
    except Exception as e:
        print(f"❌ Failed with valid connection: {e}")
        return False

def test_filing_document_fix():
    """Test that filing document creation includes required filed_as_of_date."""
    print("\nTesting filing document fix...")
    
    db_url = os.getenv('DATABASE_URL', '***********************************************/myapp')
    
    try:
        with SECDBManager(db_url) as db:
            # First, we need a company and filing to test with
            # Add a test company
            success = db.add_company(
                cik="0000999999",
                company_name="Test Corp for Document Fix",
                ticker_symbol="TESTD"
            )
            
            if success:
                print("✅ Test company added")
                
                # Add a test filing
                filing_id = db.add_filing(
                    accession_number="0000999999-24-000001",
                    submission_type="10-K",
                    filed_date="2024-01-15",
                    company_name="Test Corp for Document Fix",
                    company_cik="0000999999"
                )
                
                if filing_id:
                    print(f"✅ Test filing added with ID: {filing_id}")
                    
                    # Now test adding a document (this should automatically get filed_as_of_date)
                    doc_id = db.add_filing_document(
                        filing_id=filing_id,
                        document_type="10-K",
                        filename="test-10k.htm",
                        description="Test 10-K document"
                    )
                    
                    if doc_id:
                        print(f"✅ Filing document added with ID: {doc_id}")
                        
                        # Verify the document has the filed_as_of_date
                        doc = db.get_filing_document(doc_id)
                        if doc and doc.get('filed_as_of_date'):
                            print(f"✅ Document has filed_as_of_date: {doc['filed_as_of_date']}")
                            return True
                        else:
                            print("❌ Document missing filed_as_of_date")
                            return False
                    else:
                        print("❌ Failed to add filing document")
                        return False
                else:
                    print("❌ Failed to add test filing")
                    return False
            else:
                print("❌ Failed to add test company")
                return False
                
    except Exception as e:
        print(f"❌ Error during filing document test: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing CRUD fixes...")
    
    test1_passed = test_connection_handling()
    test2_passed = test_filing_document_fix()
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The fixes are working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
        sys.exit(1)
