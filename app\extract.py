import re
from lxml import html

def is_probably_html(text: str) -> bool:
    text = text.strip()
    if '<' not in text or '>' not in text:
        return False
    html_tag_pattern = re.compile(
        r'</?(html|head|body|div|span|p|a|script|style|table|tr|td|ul|li|h[1-6]|meta|title|br|input|form)[\s>/]',
        re.IGNORECASE
    )
    return bool(html_tag_pattern.search(text))


def extract_visible_text(html_text: str) -> str:
    if html is None:
        return "lxml not available"

    try:
        tree = html.fromstring(html_text)

        for tag in tree.xpath('//script | //style | //noscript | //head | //title | //meta'):
            parent = tag.getparent()
            if parent is not None:
                parent.remove(tag)

        for el in tree.xpath('//*[@style]'):
            style = el.attrib.get('style', '').replace(' ', '').lower()
            if 'display:none' in style or 'visibility:hidden' in style:
                parent = el.getparent()
                if parent is not None:
                    parent.remove(el)

        text_nodes = tree.xpath('//text()[normalize-space()]')
        return ' '.join(text.strip() for text in text_nodes)

    except Exception as e:
        return f"Error parsing HTML: {e}"

# Define simple header and document types
SECHeader = dict[str, str]
SECDocument = dict[str, str]

def parse_sec_file(filepath: str) -> tuple[SECHeader, list[SECDocument]]:
    sec_header_patterns: list[tuple[str, re.Pattern[bytes]]] = [
        ('acceptance_datetime', re.compile(rb"\s*<ACCEPTANCE-DATETIME>\s*(\d{14})")),
        ('accession_number', re.compile(rb"\s*ACCESSION NUMBER:\s*(\S+)")),
        ('conformed_submission_type', re.compile(rb"\s*CONFORMED SUBMISSION TYPE:\s*(\S+)")),
        ('public_document_count', re.compile(rb"\s*PUBLIC DOCUMENT COUNT:\s*(\d+)")),
        ('conformed_period_of_report', re.compile(rb"\s*CONFORMED PERIOD OF REPORT:\s*(\d{8})")),
        ('filing_date', re.compile(rb"\s*FILED AS OF DATE:\s*(\d{8})")),
        ('amendment_date', re.compile(rb"\s*DATE AS OF CHANGE:\s*(\d{8})")),
        ('company_conformed_name', re.compile(rb"\s*COMPANY CONFORMED NAME:\s*(.+)")),
        ('cik', re.compile(rb"\s*CENTRAL INDEX KEY:\s*(\d+)")),
    ]

    document_patterns: list[tuple[str, re.Pattern[bytes]]] = [
        ('document_type', re.compile(rb"\s*<TYPE>\s*(\S+)")),
        ('filename', re.compile(rb"\s*<FILENAME>\s*(\S+)")),
        ('description', re.compile(rb"\s*<DESCRIPTION>\s*(.+)")),
    ]

    document_start_pattern = re.compile(rb"<DOCUMENT>")
    document_end_pattern = re.compile(rb"</DOCUMENT>")

    results: SECHeader = {}
    documents: list[SECDocument] = []

    current_location = 'header'
    in_document = False
    temp_document: SECDocument = {}
    temp_document_text = ""

    header_index = 0
    doc_index = 0

    with open(filepath, 'rb') as f:
        for line in f:
            if current_location == 'header' and header_index < len(sec_header_patterns):
                key, pattern = sec_header_patterns[header_index]
                match = pattern.match(line)
                if match:
                    results[key] = match.group(1).decode()
                    header_index += 1
                    if header_index >= len(sec_header_patterns):
                        current_location = 'documents'

            elif current_location == 'documents':
                if in_document:
                    if document_end_pattern.match(line):
                        temp_document['text'] = temp_document_text
                        documents.append(temp_document)
                        temp_document = {}
                        temp_document_text = ""
                        in_document = False
                        doc_index = 0
                    elif doc_index < len(document_patterns):
                        key, pattern = document_patterns[doc_index]
                        match = pattern.match(line)
                        if match:
                            temp_document[key] = match.group(1).decode()
                            doc_index += 1
                    else:
                        try:
                            temp_document_text += line.decode()
                        except UnicodeDecodeError:
                            continue  # ignore binary junk
                elif document_start_pattern.match(line):
                    in_document = True
                    temp_document = {}
                    temp_document_text = ""

    return results, documents


# Example use
if __name__ == '__main__':
    results, documents = parse_sec_file("a:/DataAggregator/PR/extract/data/CBZ_8-K_20060523_Agreement+Events+Exhibits.txt")
    print(results)
    if len(documents) > 1:
        print(documents[1])
